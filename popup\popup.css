/* CSS变量定义 */
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #e2e8f0;
  
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  
  --border-color: #e2e8f0;
  --border-radius: 8px;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  
  --transition: all 0.2s ease-in-out;
}

/* 暗色主题 */
[data-theme="dark"] {
  --bg-primary: #1e293b;
  --bg-secondary: #334155;
  --bg-tertiary: #475569;
  
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  
  --border-color: #475569;
}

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background: var(--bg-primary);
  width: 400px;
  min-height: 500px;
  overflow: hidden;
}

/* 主容器 */
.translator-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 600px;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.icon {
  font-size: 18px;
}

.theme-toggle {
  display: flex;
}

.theme-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-secondary);
}

.theme-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* 语言选择器 */
.language-selector {
  padding: 16px 20px;
  background: var(--bg-primary);
}

.language-pair {
  display: flex;
  align-items: center;
  gap: 12px;
}

.language-select {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition);
}

.language-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.swap-btn {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  padding: 10px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-secondary);
}

.swap-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transform: rotate(180deg);
}

/* 文本区域 */
.text-area {
  flex: 1;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.input-section,
.output-section {
  position: relative;
}

.text-input,
.text-output {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  transition: var(--transition);
}

.text-input {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.text-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.text-output {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-style: dashed;
  position: relative;
  overflow-y: auto;
}

.placeholder-text {
  color: var(--text-muted);
  font-style: italic;
}

.input-actions,
.output-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
}

.action-btn {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: var(--transition);
  font-size: 12px;
}

.action-btn:hover {
  background: var(--bg-tertiary);
  transform: translateY(-1px);
}

/* 翻译按钮 */
.translate-section {
  padding: 16px 20px;
}

.translate-btn {
  width: 100%;
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.translate-btn:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.translate-btn:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
  transform: none;
}

/* 底部功能区 */
.footer {
  padding: 16px 20px;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
}

.footer-actions {
  display: flex;
  justify-content: space-around;
  gap: 8px;
}

.footer-btn {
  background: none;
  border: none;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: var(--text-secondary);
  font-size: 12px;
}

.footer-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.btn-icon {
  font-size: 16px;
}

.btn-label {
  font-size: 10px;
}

/* 加载遮罩 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-secondary);
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 400px) {
  body {
    width: 100vw;
  }
  
  .header,
  .language-selector,
  .translate-section,
  .footer {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .text-area {
    padding-left: 16px;
    padding-right: 16px;
  }
}
