/**
 * Chrome翻译助手 - 弹出窗口控制器
 * 负责处理弹出窗口的用户交互和翻译功能
 */

class PopupController {
  constructor() {
    this.elements = {};
    this.currentTheme = 'light';
    this.isTranslating = false;
    
    this.init();
  }

  /**
   * 初始化弹出窗口
   */
  async init() {
    try {
      this.initElements();
      this.bindEvents();
      await this.loadSettings();
      await this.loadSelectedText();
      
      console.log('弹出窗口初始化完成');
    } catch (error) {
      console.error('弹出窗口初始化失败:', error);
      this.showError('初始化失败，请重试');
    }
  }

  /**
   * 初始化DOM元素引用
   */
  initElements() {
    this.elements = {
      // 语言选择
      sourceLanguage: document.getElementById('sourceLanguage'),
      targetLanguage: document.getElementById('targetLanguage'),
      swapLanguages: document.getElementById('swapLanguages'),
      
      // 文本区域
      sourceText: document.getElementById('sourceText'),
      translationResult: document.getElementById('translationResult'),
      
      // 操作按钮
      translateBtn: document.getElementById('translateBtn'),
      clearInput: document.getElementById('clearInput'),
      pasteText: document.getElementById('pasteText'),
      copyResult: document.getElementById('copyResult'),
      speakResult: document.getElementById('speakResult'),
      
      // 底部功能
      historyBtn: document.getElementById('historyBtn'),
      settingsBtn: document.getElementById('settingsBtn'),
      translatePageBtn: document.getElementById('translatePageBtn'),
      
      // 主题切换
      themeToggle: document.getElementById('themeToggle'),
      
      // 加载状态
      loadingOverlay: document.getElementById('loadingOverlay'),
      loadingSpinner: document.querySelector('.loading-spinner'),
      btnText: document.querySelector('.btn-text')
    };
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 翻译按钮
    this.elements.translateBtn.addEventListener('click', () => this.handleTranslate());
    
    // 语言交换
    this.elements.swapLanguages.addEventListener('click', () => this.swapLanguages());
    
    // 文本操作
    this.elements.clearInput.addEventListener('click', () => this.clearInput());
    this.elements.pasteText.addEventListener('click', () => this.pasteText());
    this.elements.copyResult.addEventListener('click', () => this.copyResult());
    this.elements.speakResult.addEventListener('click', () => this.speakResult());
    
    // 底部功能按钮
    this.elements.historyBtn.addEventListener('click', () => this.showHistory());
    this.elements.settingsBtn.addEventListener('click', () => this.showSettings());
    this.elements.translatePageBtn.addEventListener('click', () => this.translatePage());
    
    // 主题切换
    this.elements.themeToggle.addEventListener('click', () => this.toggleTheme());
    
    // 输入框变化监听
    this.elements.sourceText.addEventListener('input', () => this.onTextInput());
    
    // 语言选择变化
    this.elements.sourceLanguage.addEventListener('change', () => this.onLanguageChange());
    this.elements.targetLanguage.addEventListener('change', () => this.onLanguageChange());
    
    // 键盘快捷键
    document.addEventListener('keydown', (e) => this.handleKeyboard(e));
  }

  /**
   * 处理翻译请求
   */
  async handleTranslate() {
    const sourceText = this.elements.sourceText.value.trim();
    
    if (!sourceText) {
      this.showError('请输入要翻译的文本');
      return;
    }

    if (this.isTranslating) {
      return;
    }

    try {
      this.setTranslating(true);
      
      const translationRequest = {
        text: sourceText,
        from: this.elements.sourceLanguage.value,
        to: this.elements.targetLanguage.value
      };

      // 发送翻译请求到background script
      const response = await this.sendMessage({
        action: 'translate',
        data: translationRequest
      });

      if (response.success) {
        this.displayTranslation(response.data);
        await this.saveToHistory(translationRequest, response.data);
      } else {
        throw new Error(response.error || '翻译失败');
      }
      
    } catch (error) {
      console.error('翻译错误:', error);
      this.showError(error.message || '翻译失败，请重试');
    } finally {
      this.setTranslating(false);
    }
  }

  /**
   * 显示翻译结果
   */
  displayTranslation(result) {
    const resultElement = this.elements.translationResult;
    const placeholder = resultElement.querySelector('.placeholder-text');
    
    if (placeholder) {
      placeholder.remove();
    }
    
    resultElement.innerHTML = `
      <div class="translation-text">${result.translatedText}</div>
      ${result.detectedLanguage ? `<div class="detected-language">检测到语言: ${result.detectedLanguage}</div>` : ''}
    `;
    
    // 添加复制和朗读按钮的可用状态
    this.elements.copyResult.disabled = false;
    this.elements.speakResult.disabled = false;
  }

  /**
   * 设置翻译状态
   */
  setTranslating(isTranslating) {
    this.isTranslating = isTranslating;
    
    if (isTranslating) {
      this.elements.translateBtn.disabled = true;
      this.elements.btnText.style.display = 'none';
      this.elements.loadingSpinner.style.display = 'inline-block';
      this.showLoadingOverlay();
    } else {
      this.elements.translateBtn.disabled = false;
      this.elements.btnText.style.display = 'inline-block';
      this.elements.loadingSpinner.style.display = 'none';
      this.hideLoadingOverlay();
    }
  }

  /**
   * 交换源语言和目标语言
   */
  swapLanguages() {
    const sourceValue = this.elements.sourceLanguage.value;
    const targetValue = this.elements.targetLanguage.value;
    
    if (sourceValue === 'auto') {
      this.showError('自动检测语言无法交换');
      return;
    }
    
    this.elements.sourceLanguage.value = targetValue;
    this.elements.targetLanguage.value = sourceValue;
    
    // 如果有文本，自动重新翻译
    if (this.elements.sourceText.value.trim()) {
      this.handleTranslate();
    }
  }

  /**
   * 清除输入文本
   */
  clearInput() {
    this.elements.sourceText.value = '';
    this.elements.translationResult.innerHTML = '<div class="placeholder-text">翻译结果将显示在这里...</div>';
    this.elements.copyResult.disabled = true;
    this.elements.speakResult.disabled = true;
    this.elements.sourceText.focus();
  }

  /**
   * 粘贴文本
   */
  async pasteText() {
    try {
      const text = await navigator.clipboard.readText();
      this.elements.sourceText.value = text;
      this.onTextInput();
    } catch (error) {
      console.error('粘贴失败:', error);
      this.showError('粘贴失败，请手动输入');
    }
  }

  /**
   * 复制翻译结果
   */
  async copyResult() {
    const resultText = this.elements.translationResult.querySelector('.translation-text');
    if (!resultText) {
      this.showError('没有可复制的内容');
      return;
    }

    try {
      await navigator.clipboard.writeText(resultText.textContent);
      this.showSuccess('已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
      this.showError('复制失败');
    }
  }

  /**
   * 朗读翻译结果
   */
  speakResult() {
    const resultText = this.elements.translationResult.querySelector('.translation-text');
    if (!resultText) {
      this.showError('没有可朗读的内容');
      return;
    }

    const utterance = new SpeechSynthesisUtterance(resultText.textContent);
    utterance.lang = this.elements.targetLanguage.value;
    speechSynthesis.speak(utterance);
  }

  /**
   * 显示翻译历史
   */
  async showHistory() {
    // TODO: 实现历史记录功能
    this.showInfo('历史记录功能开发中...');
  }

  /**
   * 显示设置页面
   */
  showSettings() {
    // TODO: 实现设置功能
    this.showInfo('设置功能开发中...');
  }

  /**
   * 翻译整个页面
   */
  async translatePage() {
    try {
      const response = await this.sendMessage({
        action: 'translatePage',
        data: {
          targetLanguage: this.elements.targetLanguage.value
        }
      });

      if (response.success) {
        this.showSuccess('页面翻译已开始');
        window.close(); // 关闭弹出窗口
      } else {
        throw new Error(response.error || '页面翻译失败');
      }
    } catch (error) {
      console.error('页面翻译错误:', error);
      this.showError(error.message || '页面翻译失败');
    }
  }

  /**
   * 切换主题
   */
  toggleTheme() {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    document.body.setAttribute('data-theme', this.currentTheme);
    
    const themeIcon = this.elements.themeToggle.querySelector('.theme-icon');
    themeIcon.textContent = this.currentTheme === 'light' ? '🌙' : '☀️';
    
    // 保存主题设置
    this.saveSettings();
  }

  /**
   * 处理文本输入事件
   */
  onTextInput() {
    const hasText = this.elements.sourceText.value.trim().length > 0;
    this.elements.translateBtn.disabled = !hasText || this.isTranslating;
  }

  /**
   * 处理语言选择变化
   */
  onLanguageChange() {
    this.saveSettings();
  }

  /**
   * 处理键盘快捷键
   */
  handleKeyboard(event) {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'Enter':
          event.preventDefault();
          this.handleTranslate();
          break;
        case 'l':
          event.preventDefault();
          this.clearInput();
          break;
      }
    }
  }

  /**
   * 发送消息到background script
   */
  async sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, resolve);
    });
  }

  /**
   * 加载用户设置
   */
  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get([
        'sourceLanguage',
        'targetLanguage', 
        'theme'
      ]);
      
      if (result.sourceLanguage) {
        this.elements.sourceLanguage.value = result.sourceLanguage;
      }
      if (result.targetLanguage) {
        this.elements.targetLanguage.value = result.targetLanguage;
      }
      if (result.theme) {
        this.currentTheme = result.theme;
        document.body.setAttribute('data-theme', this.currentTheme);
        const themeIcon = this.elements.themeToggle.querySelector('.theme-icon');
        themeIcon.textContent = this.currentTheme === 'light' ? '🌙' : '☀️';
      }
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  }

  /**
   * 保存用户设置
   */
  async saveSettings() {
    try {
      await chrome.storage.sync.set({
        sourceLanguage: this.elements.sourceLanguage.value,
        targetLanguage: this.elements.targetLanguage.value,
        theme: this.currentTheme
      });
    } catch (error) {
      console.error('保存设置失败:', error);
    }
  }

  /**
   * 加载选中的文本
   */
  async loadSelectedText() {
    try {
      const response = await this.sendMessage({
        action: 'getSelectedText'
      });
      
      if (response.success && response.data) {
        this.elements.sourceText.value = response.data;
        this.onTextInput();
      }
    } catch (error) {
      console.error('加载选中文本失败:', error);
    }
  }

  /**
   * 保存到翻译历史
   */
  async saveToHistory(request, result) {
    try {
      await this.sendMessage({
        action: 'saveHistory',
        data: {
          sourceText: request.text,
          translatedText: result.translatedText,
          fromLanguage: request.from,
          toLanguage: request.to,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      console.error('保存历史失败:', error);
    }
  }

  /**
   * 显示加载遮罩
   */
  showLoadingOverlay() {
    this.elements.loadingOverlay.style.display = 'flex';
  }

  /**
   * 隐藏加载遮罩
   */
  hideLoadingOverlay() {
    this.elements.loadingOverlay.style.display = 'none';
  }

  /**
   * 显示错误消息
   */
  showError(message) {
    this.showNotification(message, 'error');
  }

  /**
   * 显示成功消息
   */
  showSuccess(message) {
    this.showNotification(message, 'success');
  }

  /**
   * 显示信息消息
   */
  showInfo(message) {
    this.showNotification(message, 'info');
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'info') {
    // 简单的通知实现，后续可以改进
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 16px;
      border-radius: 4px;
      color: white;
      font-size: 14px;
      z-index: 10000;
      animation: slideIn 0.3s ease-out;
      background: ${type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#3b82f6'};
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 3000);
  }
}

// 初始化弹出窗口
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});
